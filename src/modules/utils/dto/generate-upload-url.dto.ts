import { ENTITY_TYPES, EntityTypesType, MEDIA_TYPES_INFO } from '@/constants/posts';
import { ApiProperty } from '@nestjs/swagger';
import { IsIn, IsNotEmpty } from 'class-validator';

// DTO with Class Validator
export class GenerateUploadUrlDto {
  @ApiProperty({
    name: 'mediaType',
    enum: [
      ...MEDIA_TYPES_INFO.image.accept,
      ...MEDIA_TYPES_INFO.video.accept,
      ...MEDIA_TYPES_INFO.document.accept,
    ],
    example: MEDIA_TYPES_INFO.image.accept[0],
    required: true,
  })
  @IsNotEmpty()
  @IsIn([
    ...MEDIA_TYPES_INFO.image.accept,
    ...MEDIA_TYPES_INFO.video.accept,
    ...MEDIA_TYPES_INFO.document.accept,
  ])
  mediaType: string;

  @ApiProperty({
    name: 'entityType',
    enum: ENTITY_TYPES,
    example: ENTITY_TYPES.POST,
    required: true,
  })
  @IsNotEmpty()
  @IsIn(Object.values(ENTITY_TYPES))
  entityType: EntityTypesType;
}
