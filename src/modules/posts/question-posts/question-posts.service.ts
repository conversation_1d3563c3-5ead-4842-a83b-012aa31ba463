import { Inject, Injectable } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { eq } from 'drizzle-orm';

import * as schema from '@/db/schema';
import { questionPosts, NewQuestionPost } from '@/db/schema/question-posts';

import { itemNotFound } from '@/exceptions/common';

import { EntityName } from '@/constants/entities';

@Injectable()
export class QuestionPostsService {
  constructor(@Inject('DB_DEV') private db: PostgresJsDatabase<typeof schema>) {}

  async createQuestionPost(postData: NewQuestionPost, tx?: PostgresJsDatabase<typeof schema>) {
    const queryRunner = tx || this.db;

    // Insert the post
    const post = await queryRunner.insert(questionPosts).values(postData).returning();

    return post;
  }

  async updateQuestionPost(
    postId: string,
    postData: Partial<NewQuestionPost>,
    tx?: PostgresJsDatabase<typeof schema>,
  ) {
    const queryRunner = tx || this.db;

    const [updatedQuestionPost] = await queryRunner
      .update(questionPosts)
      .set(postData)
      .where(eq(questionPosts.postId, postId))
      .returning();

    return updatedQuestionPost;
  }

  async getQuestionPostById(postId: string) {
    const questionPost = await this.db.query.questionPosts.findFirst({
      where: eq(questionPosts.postId, postId),
      with: {
        post: {
          columns: {
            workspaceId: true,
          },
        },
      },
    });

    if (!questionPost) throw itemNotFound(EntityName.POST);

    return questionPost;
  }
}
