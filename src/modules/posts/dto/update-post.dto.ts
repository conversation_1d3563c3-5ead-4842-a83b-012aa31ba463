import { ApiProperty, OmitType, PartialType } from '@nestjs/swagger';
import { IsDate, IsOptional, IsString, MaxLength, MinLength, ValidateIf } from 'class-validator';
import { Transform, TransformFnParams } from 'class-transformer';

import { CommonPostCreateDto, IsFutureDateIfScheduled } from './create-post.dto';

import { MAX_LENGTH, MIN_LENGTH, PostStatus } from '@/constants/posts';

export class UpdateCommonPostCreateDto extends PartialType(
  OmitType(CommonPostCreateDto, ['postScheduleDate']),
) {
  @ApiProperty({
    name: 'postScheduleDate',
    type: 'string',
    required: false, // marked as not required here but validated conditionally
    example: '2024-12-31T23:59:59Z',
    description:
      'Required when postStatus is SCHEDULED. If publishing immediately, current date will be used.',
  })
  @ValidateIf((obj) => obj.postStatus === PostStatus.SCHEDULED)
  @IsDate()
  @IsFutureDateIfScheduled()
  @Transform(({ value }) => {
    if (value) {
      return new Date(value);
    }
  })
  postScheduleDate?: Date;

  @ApiProperty({
    name: 'content',
    type: 'string',
    required: true,
    example: 'hello world is a great place to start',
  })
  @IsOptional()
  @Transform(({ value }: TransformFnParams) => value?.trim())
  @IsString()
  @MinLength(MIN_LENGTH)
  @MaxLength(MAX_LENGTH)
  content?: string;
}
