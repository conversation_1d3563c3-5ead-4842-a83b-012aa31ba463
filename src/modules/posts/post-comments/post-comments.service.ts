import { Inject, Injectable } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { and, eq } from 'drizzle-orm';

import * as schema from '@/db/schema';
import { postComments, posts, questionPosts } from '@/db/schema';

import { CreateOrUpdatePostCommentDto } from './dto/create-post-comment';

import { PostCommentsStatus } from '@/constants/post-comments';
import { EntityName } from '@/constants/entities';
import { EntityType } from '@/constants/user-types';
import { PostActiveStatus } from '@/constants/posts';

import { itemNotFound } from '@/exceptions/common';

@Injectable()
export class PostCommentsService {
  constructor(@Inject('DB_DEV') private db: PostgresJsDatabase<typeof schema>) {}

  async createPostComment(
    postId: string,
    entityId: string,
    entityType: EntityType,
    comment: CreateOrUpdatePostCommentDto['comment'],
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const db = transaction || this.db;

    const [newComment] = await db
      .insert(postComments)
      .values({
        postId,
        entityId,
        entityType,
        comment,
        status: PostCommentsStatus.ACTIVE,
      })
      .returning();

    return newComment;
  }

  async updatePostComment(
    commentId: string,
    entityId: string,
    comment: CreateOrUpdatePostCommentDto['comment'],
  ) {
    const [res] = await this.db
      .update(postComments)
      .set({
        comment,
      })
      .where(
        and(
          eq(postComments.id, commentId),
          eq(postComments.entityId, entityId),
          eq(postComments.status, PostCommentsStatus.ACTIVE),
        ),
      )
      .returning();

    if (!res) throw itemNotFound(EntityName.POST_COMMENT);

    return res;
  }

  async softDeletePostComment(
    id: string,
    entityId: string,
    transaction?: PostgresJsDatabase<typeof schema>,
  ) {
    const db = transaction || this.db;

    const [res] = await db
      .update(postComments)
      .set({
        status: PostCommentsStatus.INACTIVE,
      })
      .where(
        and(
          eq(postComments.id, id),
          eq(postComments.status, PostCommentsStatus.ACTIVE),
          eq(postComments.entityId, entityId),
        ),
      )
      .returning();

    if (!res) throw itemNotFound(EntityName.POST_COMMENT);

    return res;
  }

  async getCommentWithPostDetails(commentId: string) {
    const [commentDetails] = await this.db
      .select({
        postId: postComments.postId,
        postType: posts.postType,
        workspaceId: posts.workspaceId,
        pinnedCommentId: questionPosts.pinnedCommentId,
      })
      .from(postComments)
      .innerJoin(posts, eq(postComments.postId, posts.id))
      .innerJoin(questionPosts, eq(posts.id, questionPosts.postId))
      .where(
        and(
          eq(postComments.id, commentId),
          eq(postComments.status, PostCommentsStatus.ACTIVE),
          eq(posts.status, PostActiveStatus.ACTIVE),
        ),
      )
      .limit(1);

    return commentDetails;
  }
}
