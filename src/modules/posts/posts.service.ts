import { Inject, Injectable } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { and, eq, exists, gte, ilike, inArray, lte, ne, or, sql } from 'drizzle-orm';
import { desc, asc, isNotNull } from 'drizzle-orm/expressions';

import * as schema from '@/db/schema';
import {
  posts,
  NewPost,
  tags,
  postTags,
  workspaces,
  users,
  postLikes,
  postMedias,
  postComments,
  questionPosts,
} from '@/db/schema';
import { postCommentLikes } from '@/db/schema/post-comment-likes';

import { countRelation, isLikedByEntity } from '@/utils/database';

import { EntityName } from '@/constants/entities';
import { PostTagsStatus } from '@/constants/post-tags';
import { PrimarySpeciality } from '@/constants/workspaces';
import { PostActiveStatus, PostStatus, PostType, PostTypeJoinFields } from '@/constants/posts';
import { PostLikesStatus } from '@/constants/post-likes';
import { PostCommentsStatus } from '@/constants/post-comments';
import { PostMediaStatus } from '@/constants/post-media';
import { EntityType } from '@/constants/user-types';
import { PostCommentLikesStatus } from '@/constants/post-comment-likes';

import { audiencePermissionError } from '@/exceptions/posts';
import { itemNotFound } from '@/exceptions/common';

import { CreatorDetails } from '@/interfaces/post';

@Injectable()
export class PostsService {
  constructor(@Inject('DB_DEV') private db: PostgresJsDatabase<typeof schema>) {}

  /**
   * Get the most relevant comment for a question post based on the following criteria:
   * 1. If there's a pinned comment, return it
   * 2. If no pinned comment, return the comment with the most likes
   * 3. If multiple comments have the same number of likes, return the one that reached that count first
   * 4. If no likes, return the most recent comment
   * 5. If no comments, return null
   */
  /**
   * Batch fetch featured comments for multiple question posts
   */
  private async batchGetFeaturedComments(postIds: string[]): Promise<Map<string, any>> {
    // First, get all pinned comments
    const pinnedCommentsMap = await this.batchGetPinnedComments(postIds);

    // Get post IDs that don't have pinned comments
    const postsWithoutPinnedComments = postIds.filter((id) => !pinnedCommentsMap.has(id));

    // Get most relevant comments for posts without pinned comments
    const relevantCommentsMap =
      postsWithoutPinnedComments.length > 0
        ? await this.batchGetMostRelevantComments(postsWithoutPinnedComments)
        : new Map();

    // Merge the results
    return new Map([...pinnedCommentsMap, ...relevantCommentsMap]);
  }

  /**
   * Batch fetch pinned comments for multiple posts
   */
  private async batchGetPinnedComments(postIds: string[]): Promise<Map<string, any>> {
    const pinnedCommentsData = await this.db
      .select({
        postId: questionPosts.postId,
        commentId: postComments.id,
        commentCreatorId: postComments.entityId,
        commentCreatorEntity: postComments.entityType,
        comment: postComments.comment,
        commentCreatedAt: postComments.createdAt,
      })
      .from(questionPosts)
      .innerJoin(
        postComments,
        and(
          eq(questionPosts.pinnedCommentId, postComments.id),
          eq(postComments.status, PostCommentsStatus.ACTIVE),
        ),
      )
      .where(and(inArray(questionPosts.postId, postIds), isNotNull(questionPosts.pinnedCommentId)));

    // Get all unique creator IDs for batch creator lookup
    const creatorIds = pinnedCommentsData.map((comment) => ({
      id: comment.commentCreatorId,
      entityType: comment.commentCreatorEntity,
    }));

    const creatorsMap = await this.batchGetCommentCreators(creatorIds);

    const pinnedCommentsMap = new Map();

    for (const commentData of pinnedCommentsData) {
      const creatorKey = `${commentData.commentCreatorEntity}:${commentData.commentCreatorId}`;
      const creator = creatorsMap.get(creatorKey);

      if (creator) {
        pinnedCommentsMap.set(commentData.postId, {
          id: commentData.commentId,
          content: commentData.comment,
          creator,
          isPinned: true,
          createdAt: commentData.commentCreatedAt,
        });
      }
    }

    return pinnedCommentsMap;
  }

  /**
   * Batch fetch most relevant comments for posts without pinned comments
   */
  private async batchGetMostRelevantComments(postIds: string[]): Promise<Map<string, any>> {
    const commentsWithLikes = await this.db
      .select({
        postId: postComments.postId,
        commentId: postComments.id,
        comment: postComments.comment,
        createdAt: postComments.createdAt,
        commentCreatorId: postComments.entityId,
        commentCreatorEntity: postComments.entityType,
        likesCount: sql<number>`coalesce(count(${postCommentLikes.postCommentId}), 0)::int`,
        // Use the latest updatedAt (or createdAt if no updates) of active likes to determine when the comment reached its current like count
        latestLikeTime: sql<Date>`coalesce(max(coalesce(${postCommentLikes.updatedAt}, ${postCommentLikes.createdAt})), ${postComments.createdAt})`,
      })
      .from(postComments)
      .leftJoin(
        postCommentLikes,
        and(
          eq(postCommentLikes.postCommentId, postComments.id),
          eq(postCommentLikes.status, PostCommentLikesStatus.ACTIVE),
        ),
      )
      .where(
        and(
          inArray(postComments.postId, postIds),
          eq(postComments.status, PostCommentsStatus.ACTIVE),
        ),
      )
      .groupBy(postComments.id, postComments.postId)
      .orderBy(
        asc(postComments.postId), // Group by post
        desc(sql`coalesce(count(${postCommentLikes.postCommentId}), 0)`), // Most likes first
        asc(
          sql`coalesce(max(coalesce(${postCommentLikes.updatedAt}, ${postCommentLikes.createdAt})), ${postComments.createdAt})`,
        ), // Earliest to reach current like count
      );

    // Group comments by post ID and select most relevant for each
    const commentsByPost = new Map<string, typeof commentsWithLikes>();

    for (const comment of commentsWithLikes) {
      if (!commentsByPost.has(comment.postId)) {
        commentsByPost.set(comment.postId, []);
      }
      commentsByPost.get(comment.postId)!.push(comment);
    }

    // Get all unique creator IDs for batch creator lookup
    const creatorIds = commentsWithLikes.map((comment) => ({
      id: comment.commentCreatorId,
      entityType: comment.commentCreatorEntity,
    }));

    const creatorsMap = await this.batchGetCommentCreators(creatorIds);

    const relevantCommentsMap = new Map();

    for (const [postId, commentsForPost] of commentsByPost) {
      const selectedComment = this.selectMostRelevantFromBatch(commentsForPost);
      const creatorKey = `${selectedComment.commentCreatorEntity}:${selectedComment.commentCreatorId}`;
      const creator = creatorsMap.get(creatorKey);

      if (creator && selectedComment) {
        const topLikesCount = selectedComment.likesCount;
        const hasMultipleCommentsWithSameLikes =
          commentsForPost.length > 1 && topLikesCount === commentsForPost[1].likesCount;

        relevantCommentsMap.set(postId, {
          id: selectedComment.commentId,
          content: selectedComment.comment,
          creator,
          hasMoreThanOneAnswer: hasMultipleCommentsWithSameLikes,
          commentHasLikes: topLikesCount > 0,
          isPinned: false,
          createdAt: selectedComment.createdAt,
        });
      }
    }

    return relevantCommentsMap;
  }

  /**
   * Select most relevant comment from batch results
   */
  private selectMostRelevantFromBatch(commentsForPost: any[]) {
    if (commentsForPost.length === 0) return null;

    // Comments are already sorted by:
    // 1. Most likes first (DESC)
    // 2. Earliest to reach current like count (ASC on latestLikeTime)

    const topComment = commentsForPost[0];
    const topLikesCount = topComment.likesCount;

    // If no likes on the top comment, return the most recent comment
    if (topLikesCount === 0) {
      return commentsForPost.reduce((mostRecent, comment) =>
        new Date(comment.createdAt) > new Date(mostRecent.createdAt) ? comment : mostRecent,
      );
    }

    // If there are likes, the query already sorted correctly:
    // - Most likes first
    // - Among comments with same likes, earliest to reach that count first
    return topComment;
  }

  /**
   * Batch fetch comment creators (users and workspace creators)
   */
  private async batchGetCommentCreators(
    creatorIds: { id: string; entityType: string }[],
  ): Promise<Map<string, CreatorDetails>> {
    const creatorsMap = new Map<string, CreatorDetails>();

    // Separate user and workspace creator IDs
    const userIds = creatorIds
      .filter((creator) => creator.entityType === EntityType.USER)
      .map((creator) => creator.id);

    const workspaceIds = creatorIds
      .filter((creator) => creator.entityType === EntityType.WORKSPACE)
      .map((creator) => creator.id);

    // Batch fetch users
    if (userIds.length > 0) {
      const userResults = await this.db.query.users.findMany({
        columns: {
          id: true,
          profileImageUrlThumbnail: true,
          username: true,
          displayName: true,
        },
        where: inArray(users.id, userIds),
      });

      for (const userResult of userResults) {
        creatorsMap.set(`${EntityType.USER}:${userResult.id}`, userResult);
      }
    }

    // Batch fetch workspace creators
    if (workspaceIds.length > 0) {
      const workspaceResults = await this.db.query.workspaces.findMany({
        columns: {
          id: true,
        },
        where: inArray(workspaces.id, workspaceIds),
        with: {
          createdByUser: {
            columns: {
              username: true,
              id: true,
              profileImageUrlThumbnail: true,
              displayName: true,
            },
          },
        },
      });

      for (const workspaceResult of workspaceResults) {
        creatorsMap.set(
          `${EntityType.WORKSPACE}:${workspaceResult.id}`,
          workspaceResult.createdByUser,
        );
      }
    }

    return creatorsMap;
  }

  async findAll({
    limit,
    offset,
    entityId,
    searchKeyword,
    postTypes,
  }: {
    limit: number;
    offset: number;
    entityId: string;
    searchKeyword?: string;
    postTypes?: PostType[];
  }) {
    // Normalize the keyword by converting to lowercase and removing all spaces
    const normalizedKeyword = searchKeyword?.toLowerCase().replace(/\s+/g, '');

    // Create condition to match posts by tag name or publisher display name
    const keywordFilter = normalizedKeyword
      ? or(
          // Match tags
          exists(
            this.db
              .select()
              .from(postTags)
              .where(
                and(
                  eq(postTags.postId, posts.id),
                  eq(postTags.status, PostTagsStatus.ACTIVE),
                  exists(
                    this.db
                      .select()
                      .from(tags)
                      .where(
                        and(
                          eq(tags.id, postTags.tagId),
                          ilike(tags.name, `%${normalizedKeyword}%`),
                        ),
                      ),
                  ),
                ),
              ),
          ),
          // Match publisher's display name (without spaces)
          exists(
            this.db
              .select()
              .from(workspaces)
              .where(
                and(
                  eq(workspaces.id, posts.workspaceId),
                  exists(
                    this.db
                      .select()
                      .from(users)
                      .where(
                        and(
                          eq(users.id, workspaces.createdById),
                          sql`LOWER(REPLACE(${users.displayName}, ' ', '')) LIKE ${`%${normalizedKeyword}%`}`,
                        ),
                      ),
                  ),
                ),
              ),
          ),
          // Match post content using full-text search
          sql`${posts.contentTsv} @@ plainto_tsquery('english', ${normalizedKeyword})`,
        )
      : undefined;

    const postTypeFilter = postTypes?.length ? inArray(posts.postType, postTypes) : undefined;

    const rawPosts = await this.db.query.posts.findMany({
      columns: {
        id: true,
        postType: true,
        tags: true,
        content: true,
      },
      where: and(
        lte(posts.postScheduleDate, new Date()),
        eq(posts.status, PostActiveStatus.ACTIVE),
        ne(posts.postStatus, PostStatus.DRAFT),
        keywordFilter,
        postTypeFilter,
      ),
      orderBy: (post, { desc: descending }) => [descending(post.postScheduleDate)],
      with: {
        articlePost: {
          columns: {
            title: true,
            coverImagePath: true,
          },
        },
        workspace: {
          columns: {},
          with: {
            createdByUser: {
              columns: {
                displayName: true,
                username: true,
                profileImageUrlThumbnail: true,
              },
            },
          },
        },
        postMedias: {
          columns: {
            mediaPath: true,
            mediaType: true,
            altText: true,
          },
          where: eq(postMedias.status, PostMediaStatus.ACTIVE),
          orderBy: asc(postMedias.order),
        },
      },
      extras: (fields) => ({
        postedAt: sql`${posts.postScheduleDate}`.as('posted_at'),
        ...isLikedByEntity('isLiked', fields.id, entityId),
        ...countRelation(
          'commentsCount',
          fields.id,
          postComments.postId,
          PostCommentsStatus.ACTIVE,
        ),
        ...countRelation('likesCount', fields.id, postLikes.postId, PostLikesStatus.ACTIVE),
        // TODO: update once the SHARE, & REPOST tracking is added
        shareCount: sql<number>`0`.as('share_count'),
        repostCount: sql<number>`0`.as('repost_count'),
      }),
      limit,
      offset,
    });

    // Extract question post IDs for batch comment retrieval
    const questionPostIds = rawPosts
      .filter((post) => post.postType === PostType.QUESTION)
      .map((post) => post.id);

    // Batch fetch all relevant comments for question posts
    const featuredComments =
      questionPostIds.length > 0 ? await this.batchGetFeaturedComments(questionPostIds) : new Map();

    // Transform each post for frontend response
    const postsWithComments = rawPosts.map(async (dbPost) => {
      type Post = typeof dbPost;

      const post: Omit<Post, 'workspace'> & Partial<Pick<Post, 'workspace'>> = {
        ...dbPost,
      };

      // Extract publisher info from workspace relation
      const { displayName: publisherName, ...publisherDetails } = dbPost.workspace.createdByUser;
      delete post.workspace;

      // Extract only the relevant post-type data
      let flattenedPostTypeData: Record<string, any> = {};
      let featuredComment = null;

      for (const [postTypeKey, fieldKey] of Object.entries(PostTypeJoinFields)) {
        const typedFieldKey = fieldKey as keyof Post;
        if (post.postType === postTypeKey) {
          flattenedPostTypeData = post[typedFieldKey] as Record<string, any>;

          // For question posts, get the pre-fetched featured comment
          if (post.postType === PostType.QUESTION) {
            featuredComment = featuredComments.get(post.id) || null;
          }

          delete post[typedFieldKey];
        } else {
          delete post[typedFieldKey];
        }
      }

      return {
        ...post,
        ...flattenedPostTypeData,
        ...(post.postType === PostType.QUESTION ? { featuredComment } : {}),
        ...publisherDetails,
        publisherName,
      };
    });

    return Promise.all(postsWithComments);
  }

  async findOne({ postId, entityId }: { postId: string; entityId: string }) {
    const rawPost = await this.db.query.posts.findFirst({
      columns: {
        id: true,
        postType: true,
        tags: true,
        content: true,
      },
      where: and(
        eq(posts.id, postId),
        lte(posts.postScheduleDate, new Date()),
        eq(posts.status, PostActiveStatus.ACTIVE),
        ne(posts.postStatus, PostStatus.DRAFT),
      ),
      with: {
        articlePost: {
          columns: {
            title: true,
            body: true,
            coverImagePath: true,
          },
        },
        workspace: {
          columns: {},
          with: {
            createdByUser: {
              columns: {
                displayName: true,
                username: true,
                profileImageUrlThumbnail: true,
              },
            },
          },
        },
        postMedias: {
          columns: {
            mediaPath: true,
            mediaType: true,
            altText: true,
          },
          where: eq(postMedias.status, PostMediaStatus.ACTIVE),
          orderBy: asc(postMedias.order),
        },
      },
      extras: (fields) => ({
        postedAt: sql`${posts.postScheduleDate}`.as('postedAt'),
        ...isLikedByEntity('isLiked', fields.id, entityId),
        ...countRelation(
          'commentsCount',
          fields.id,
          postComments.postId,
          PostCommentsStatus.ACTIVE,
        ),
        ...countRelation('likesCount', fields.id, postLikes.postId, PostLikesStatus.ACTIVE),
        // TODO: update once the SHARE, & REPOST tracking is added
        shareCount: sql<number>`0`.as('share_count'),
        repostCount: sql<number>`0`.as('repost_count'),
      }),
    });

    if (!rawPost) throw itemNotFound(EntityName.POST);

    // For question posts, fetch the featured comment
    let featuredComment = null;
    if (rawPost.postType === PostType.QUESTION) {
      const featuredComments = await this.batchGetFeaturedComments([rawPost.id]);
      featuredComment = featuredComments.get(rawPost.id) || null;
    }

    // Transform each post for frontend response
    type Post = typeof rawPost;

    // Clone the post while making workspace optional for safe deletion
    const post: Omit<Post, 'workspace'> & Partial<Pick<Post, 'workspace'>> = rawPost;

    // Extract publisher info from workspace relation
    const { displayName: publisherName, ...publisherDetails } = rawPost.workspace.createdByUser;

    delete post.workspace;

    // Extract only the relevant post-type data (articlePost, questionPost, etc.)
    let flattenedPostTypeData: Record<string, any> = {};

    for (const [postTypeKey, fieldKey] of Object.entries(PostTypeJoinFields)) {
      const typedFieldKey = fieldKey as keyof Post;
      if (post.postType === postTypeKey) {
        flattenedPostTypeData = post[typedFieldKey] as Record<string, any>;
        delete post[typedFieldKey];
      } else {
        delete post[typedFieldKey];
      }
    }

    return {
      ...post,
      ...flattenedPostTypeData,
      ...(post.postType === PostType.QUESTION ? { featuredComment } : {}),
      ...publisherDetails,
      publisherName,
    };
  }

  async findAllByTag({
    tagName,
    limit,
    offset,
    entityId,
    searchKeyword,
    postTypes,
  }: {
    tagName: string;
    limit: number;
    offset: number;
    entityId: string;
    searchKeyword?: string;
    postTypes?: PostType[];
  }) {
    // Normalize the keyword by converting to lowercase and removing all spaces
    const normalizedKeyword = searchKeyword?.toLowerCase().replace(/\s+/g, '');

    // For tag endpoint, searchKeyword matches against publisher names and post content (not tags)
    const keywordFilter = normalizedKeyword
      ? or(
          // Match publisher's display name (without spaces)
          exists(
            this.db
              .select()
              .from(workspaces)
              .where(
                and(
                  eq(workspaces.id, posts.workspaceId),
                  exists(
                    this.db
                      .select()
                      .from(users)
                      .where(
                        and(
                          eq(users.id, workspaces.createdById),
                          sql`LOWER(REPLACE(${users.displayName}, ' ', '')) LIKE ${`%${normalizedKeyword}%`}`,
                        ),
                      ),
                  ),
                ),
              ),
          ),
          // Match post content using full-text search
          sql`${posts.contentTsv} @@ plainto_tsquery('english', ${normalizedKeyword})`,
        )
      : undefined;

    const postTypeFilter = postTypes?.length ? inArray(posts.postType, postTypes) : undefined;

    // Filter posts by the specific tag
    const tagFilter = exists(
      this.db
        .select()
        .from(postTags)
        .where(
          and(
            eq(postTags.postId, posts.id),
            eq(postTags.status, PostTagsStatus.ACTIVE),
            exists(
              this.db
                .select()
                .from(tags)
                .where(
                  and(
                    eq(tags.id, postTags.tagId),
                    sql`LOWER(${tags.name}) = ${tagName.toLowerCase()}`,
                  ),
                ),
            ),
          ),
        ),
    );

    const rawPosts = await this.db.query.posts.findMany({
      columns: {
        id: true,
        postType: true,
        tags: true,
        content: true,
      },
      where: and(
        lte(posts.postScheduleDate, new Date()),
        eq(posts.status, PostActiveStatus.ACTIVE),
        ne(posts.postStatus, PostStatus.DRAFT),
        tagFilter,
        keywordFilter,
        postTypeFilter,
      ),
      orderBy: desc(posts.postScheduleDate),
      with: {
        articlePost: {
          columns: {
            title: true,
            coverImagePath: true,
          },
        },
        workspace: {
          columns: {},
          with: {
            createdByUser: {
              columns: {
                displayName: true,
                username: true,
                profileImageUrlThumbnail: true,
              },
            },
          },
        },
        postMedias: {
          columns: {
            mediaPath: true,
            mediaType: true,
            altText: true,
          },
          where: eq(postMedias.status, PostMediaStatus.ACTIVE),
          orderBy: asc(postMedias.order),
        },
      },
      extras: (fields) => ({
        postedAt: sql`${posts.postScheduleDate}`.as('posted_at'),
        ...isLikedByEntity('isLiked', fields.id, entityId),
        ...countRelation(
          'commentsCount',
          fields.id,
          postComments.postId,
          PostCommentsStatus.ACTIVE,
        ),
        ...countRelation('likesCount', fields.id, postLikes.postId, PostLikesStatus.ACTIVE),
        // TODO: update once the SHARE, & REPOST tracking is added
        shareCount: sql<number>`0`.as('share_count'),
        repostCount: sql<number>`0`.as('repost_count'),
      }),
      limit,
      offset,
    });

    // Extract question post IDs for batch comment retrieval
    const questionPostIds = rawPosts
      .filter((post) => post.postType === PostType.QUESTION)
      .map((post) => post.id);

    // Batch fetch all relevant comments for question posts
    const featuredComments =
      questionPostIds.length > 0 ? await this.batchGetFeaturedComments(questionPostIds) : new Map();

    // Transform each post for frontend response
    const postsWithComments = rawPosts.map(async (dbPost) => {
      type Post = typeof dbPost;

      const post: Omit<Post, 'workspace'> & Partial<Pick<Post, 'workspace'>> = {
        ...dbPost,
      };

      // Extract publisher info from workspace relation
      const { displayName: publisherName, ...publisherDetails } = dbPost.workspace.createdByUser;
      delete post.workspace;

      // Extract only the relevant post-type data
      let flattenedPostTypeData: Record<string, any> = {};
      let featuredComment = null;

      for (const [postTypeKey, fieldKey] of Object.entries(PostTypeJoinFields)) {
        const typedFieldKey = fieldKey as keyof Post;
        if (post.postType === postTypeKey) {
          flattenedPostTypeData = post[typedFieldKey] as Record<string, any>;

          // For question posts, get the pre-fetched featured comment
          if (post.postType === PostType.QUESTION) {
            featuredComment = featuredComments.get(post.id) || null;
          }

          delete post[typedFieldKey];
        } else {
          delete post[typedFieldKey];
        }
      }

      return {
        ...post,
        ...flattenedPostTypeData,
        ...(post.postType === PostType.QUESTION ? { featuredComment } : {}),
        ...publisherDetails,
        publisherName,
      };
    });

    const allPosts = await Promise.all(postsWithComments);

    // Return response with tag information
    return {
      posts: allPosts,
      // TODO: Implement tag following functionality
      isFollowed: false, // Placeholder for tag following status, Update once following functionality is added
    };
  }

  async createPost(data: NewPost, tx?: PostgresJsDatabase<typeof schema>) {
    const queryRunner = tx || this.db;
    const [post] = await queryRunner.insert(posts).values(data).returning();
    return post;
  }

  async updatePost(
    postId: string,
    updateData: Partial<NewPost>,
    updatedById: string,
    tx?: PostgresJsDatabase<typeof schema>,
  ) {
    const queryRunner = tx || this.db;

    // Verify post exists
    const existingPost = await this.getPostById(postId);

    if (!existingPost) {
      throw itemNotFound(EntityName.POST);
    }

    const [updatedPost] = await queryRunner
      .update(posts)
      .set({
        ...updateData,
        updatedById,
      })
      .where(eq(posts.id, postId))
      .returning();

    return updatedPost;
  }

  // Get a post by ID
  async getPostById(postId: string) {
    const post = await this.db.query.posts.findFirst({
      where: eq(posts.id, postId),
      with: {
        publishedBy: true,
        updatedBy: true,
        postTags: {
          with: {
            tag: true,
          },
        },
      },
    });

    if (!post) {
      throw itemNotFound(EntityName.POST);
    }

    return post;
  }

  // CHECK THE PERMISSION TO CREATE POST FOR AUDIENCE WHICH IS PASSED IN PARAMETE
  async checkAudiencePermission(
    workpsacePrimarySpeciality: PrimarySpeciality,
    audience: PrimarySpeciality,
  ) {
    // If workspace has BOTH speciality, they can choose any audience
    if (workpsacePrimarySpeciality === PrimarySpeciality.BOTH) {
      return; // Allow any audience selection
    }

    // If workspace speciality doesn't match the audience (and isn't BOTH)
    if (workpsacePrimarySpeciality !== audience) {
      throw audiencePermissionError();
    }
  }

  // Get all draft posts for a workspace
  async getDraftPosts(workspaceId: string) {
    const postsData = await this.db.query.posts.findMany({
      columns: {
        id: true,
        postType: true,
        postScheduleDate: true,
        content: true,
      },
      where: and(
        eq(posts.workspaceId, workspaceId),
        eq(posts.postStatus, PostStatus.DRAFT),
        eq(posts.status, PostActiveStatus.ACTIVE),
      ),
      with: {
        articlePost: {
          columns: {
            title: true,
          },
        },
        postMedias: {
          columns: {
            mediaPath: true,
            mediaType: true,
            altText: true,
          },
          limit: 1,
          where: eq(postMedias.status, PostMediaStatus.ACTIVE),
          orderBy: asc(postMedias.order),
          extras: {
            totalMediasCount:
              sql<number>`(select count(*) from ${schema.postMedias} where ${schema.postMedias.postId} = ${schema.posts.id})`.as(
                'total_medias_count',
              ),
          },
        },
      },
      orderBy: asc(posts.postScheduleDate),
    });

    return postsData.map((postData) => {
      const post: any = { ...postData };

      // Only flatten articlePost if present
      if (post.postType === PostType.ARTICLE && post.articlePost) {
        Object.assign(post, post.articlePost);
        delete post.articlePost;
      }

      return post;
    });
  }

  // Get all scheduled posts for a workspace
  async getScheduledPosts(workspaceId: string) {
    const now = new Date();

    const postsData = await this.db.query.posts.findMany({
      columns: {
        id: true,
        postType: true,
        postScheduleDate: true,
        content: true,
      },
      where: and(
        eq(posts.workspaceId, workspaceId),
        eq(posts.postStatus, PostStatus.SCHEDULED),
        eq(posts.status, PostActiveStatus.ACTIVE),
        gte(posts.postScheduleDate, now), // Only get posts scheduled for the future
      ),
      with: {
        articlePost: {
          columns: {
            title: true,
          },
        },
        postMedias: {
          columns: {
            mediaPath: true,
            mediaType: true,
            altText: true,
          },
          limit: 1,
          where: eq(postMedias.status, PostMediaStatus.ACTIVE),
          orderBy: asc(postMedias.order),
          extras: {
            totalMediasCount:
              sql<number>`(select count(*) from ${schema.postMedias} where ${schema.postMedias.postId} = ${schema.posts.id})`.as(
                'total_medias_count',
              ),
          },
        },
      },
      orderBy: asc(posts.postScheduleDate),
    });

    return postsData.map((postData) => {
      const post: any = { ...postData };

      // Only flatten articlePost if present
      if (post.postType === PostType.ARTICLE && post.articlePost) {
        Object.assign(post, post.articlePost);
        delete post.articlePost;
      }

      return post;
    });
  }
}
