import { Inject, Injectable } from '@nestjs/common';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { and, eq } from 'drizzle-orm';

import * as schema from '@/db/schema';
import {
  postCommentLikes,
  PostCommentLike,
  NewPostCommentLike,
} from '@/db/schema/post-comment-likes';
import { postComments } from '@/db/schema/post-comments';

import { itemAlreadyExists, itemNotFound } from '@/exceptions/common';

import { EntityType } from '@/constants/user-types';
import { PostCommentLikesStatus } from '@/constants/post-comment-likes';
import { EntityName } from '@/constants/entities';

@Injectable()
export class PostCommentLikesService {
  constructor(@Inject('DB_DEV') private db: PostgresJsDatabase<typeof schema>) {}

  async likeComment(
    postCommentId: string,
    entityId: string,
    entityType: EntityType,
  ): Promise<PostCommentLike> {
    // Check if the comment exists
    const comment = await this.db.query.postComments.findFirst({
      where: eq(postComments.id, postCommentId),
    });

    if (!comment) {
      throw itemNotFound(EntityName.POST_COMMENT);
    }

    // Check if already liked
    const existingLike = await this.db.query.postCommentLikes.findFirst({
      where: and(
        eq(postCommentLikes.postCommentId, postCommentId),
        eq(postCommentLikes.entityId, entityId),
        eq(postCommentLikes.entityType, entityType),
      ),
    });

    if (existingLike) {
      if (existingLike.status === PostCommentLikesStatus.ACTIVE) {
        throw itemAlreadyExists(EntityName.POST_COMMENT_LIKE);
      }
      // If exists but inactive, activate it
      const [updatedLike] = await this.db
        .update(postCommentLikes)
        .set({
          status: PostCommentLikesStatus.ACTIVE,
        })
        .where(
          and(
            eq(postCommentLikes.entityId, existingLike.entityId),
            eq(postCommentLikes.postCommentId, existingLike.postCommentId),
            eq(postCommentLikes.entityType, existingLike.entityType),
          ),
        )
        .returning();

      return updatedLike;
    }

    // Create new like
    const newLike: NewPostCommentLike = {
      postCommentId,
      entityId,
      entityType,
      status: PostCommentLikesStatus.ACTIVE,
    };

    const [like] = await this.db.insert(postCommentLikes).values(newLike).returning();

    return like;
  }

  async unlikeComment(commentId: string, entityId: string): Promise<PostCommentLike> {
    const [existingLike] = await this.db
      .update(postCommentLikes)
      .set({
        status: PostCommentLikesStatus.INACTIVE,
      })
      .where(
        and(
          eq(postCommentLikes.postCommentId, commentId),
          eq(postCommentLikes.entityId, entityId),
          eq(postCommentLikes.status, PostCommentLikesStatus.ACTIVE),
        ),
      )
      .returning();

    if (!existingLike) {
      throw itemNotFound(EntityName.POST_COMMENT_LIKE);
    }

    return existingLike;
  }

  // async getLikesCount(commentId: string): Promise<number> {
  //   const result = await this.db
  //     .select({ count: sql<number>`count(*)::int` })
  //     .from(postCommentLikes)
  //     .where(
  //       and(
  //         eq(postCommentLikes.postCommentId, commentId),
  //         eq(postCommentLikes.status, PostCommentLikesStatus.ACTIVE),
  //       ),
  //     )
  //     .execute();

  //   return result[0]?.count || 0;
  // }

  // async hasLiked(commentId: string, entityId: string, entityType: EntityType): Promise<boolean> {
  //   const like = await this.db.query.postCommentLikes.findFirst({
  //     where: and(
  //       eq(postCommentLikes.postCommentId, commentId),
  //       eq(postCommentLikes.entityId, entityId),
  //       eq(postCommentLikes.entityType, entityType),
  //       eq(postCommentLikes.status, PostCommentLikesStatus.ACTIVE),
  //     ),
  //   });

  //   return !!like;
  // }
}
