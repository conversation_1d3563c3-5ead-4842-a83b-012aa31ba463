import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsInt, IsOptional, IsString, Max, Min } from 'class-validator';

import { DEFAULT_TAGS_LIMIT } from '@/constants/tag-follow';

const MAX_LIMIT = 50;

export class FetchTagsWithPostsDto {
  @ApiProperty({
    description: 'Search query, and this will match against the tag name',
    required: false,
  })
  @IsString()
  @IsOptional()
  @Transform(({ value }) => value?.toLowerCase().replace(/\s+/g, ''))
  searchKeyword?: string;

  @ApiProperty({
    description: 'The number of tags to skip.',
    required: false,
    default: 0,
  })
  @Type(() => Number)
  @IsInt()
  @Min(0)
  @IsOptional()
  offset: number = 0;

  @ApiProperty({
    description: `The number of tags to return. defaults to ${DEFAULT_TAGS_LIMIT}, maximum is ${MAX_LIMIT}`,
    required: false,
    default: DEFAULT_TAGS_LIMIT,
  })
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(MAX_LIMIT)
  @IsOptional()
  limit: number = DEFAULT_TAGS_LIMIT;
}
