import { <PERSON>, Post, Param, Delete, ParseUUIDPipe, Get, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { AuthConstants } from '@/constants/auth';
import { EntityType } from '@/constants/user-types';
import { EntityName } from '@/constants/entities';

import { User } from '@/decorators/user.decorator';

import { FetchTagsWithPostsDto } from './dto/fetch-tags-with-posts.dto';

import { itemNotFound } from '@/exceptions/common';

import { TagFollowService } from './tag-follow.service';
import { TagsService } from '@/modules/tags/tags.service';

@ApiTags('Tags Follow')
@Controller('tag-follow')
export class TagFollowController {
  constructor(
    private readonly tagFollowService: TagFollowService,
    private readonly tagsService: TagsService,
  ) {}

  @Post(':tagId')
  async createTagFollower(
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
    @Param('tagId', new ParseUUIDPipe({ version: '4' })) tagId: string,
  ) {
    const isTagExist = await this.tagsService.findOneTagById(tagId);

    if (!isTagExist) throw itemNotFound(EntityName.TAG);

    return this.tagFollowService.createFollower(
      workspaceId ?? userId,
      workspaceId ? EntityType.WORKSPACE : EntityType.USER,
      tagId,
    );
  }

  @Delete(':tagId')
  removeTagFollower(
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
    @Param('tagId', new ParseUUIDPipe({ version: '4' })) tagId: string,
  ) {
    return this.tagFollowService.softDeleteTagFollower(workspaceId ?? userId, tagId);
  }

  @Get('following')
  async getFollowedTagsWithPosts(
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
    @Query() query: FetchTagsWithPostsDto,
  ) {
    const entityId = workspaceId ?? userId;
    const { limit, offset, searchKeyword } = query;

    return this.tagFollowService.fetchFollowedTagsWithPosts(entityId, offset, limit, searchKeyword);
  }

  @Get('suggestions')
  async getSuggestedTagsWithPosts(
    @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
    @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
    @Query() query: FetchTagsWithPostsDto,
  ) {
    const entityId = workspaceId ?? userId;
    const { limit, offset, searchKeyword } = query;

    return this.tagFollowService.fetchSuggestedTagsWithPosts(
      entityId,
      offset,
      limit,
      searchKeyword,
    );
  }

  // @Get('follow-stats')
  // findOwnFollowStatsOfAWorkspace(
  //   @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) currUserWorkspaceId: string,
  // ) {
  //   return this.followersService.findFollowStatsOfAWorkspaceById(currUserWorkspaceId);
  // }

  // @Get('follow-stats/:workspaceId')
  // findFollowStatsOfAWorkspace(
  //   @Param('workspaceId') workspaceId: string,
  //   @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
  //   @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) currUserWorkspaceId: string,
  // ) {
  //   return this.followersService.findFollowStatsOfAWorkspaceById(
  //     workspaceId,
  //     currUserWorkspaceId ?? userId,
  //   );
  // }

  // @Get('following/:entityId')
  // findAllFollowing(@Param('entityId') entityId: string) {
  //   return this.followersService.findAllFollowingForId(entityId);
  // }

  // @Get('followers')
  // findAllOwnWorkspaceFollowers(
  //   @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  // ) {
  //   return this.followersService.findAllFollowersForId(workspaceId);
  // }

  // @Get('followers/:workspaceId')
  // findAllFollowersOfAWorkspaceById(@Param('workspaceId') workspaceId: string) {
  //   return this.followersService.findAllFollowersForId(workspaceId);
  // }

  // @Get('following/:workspaceId')
  // findiFUserFollowingParticularId(
  //   @User(AuthConstants.FIREBASE_CLAIM_USER_ID) userId: string,
  //   @User(AuthConstants.FIREBASE_CLAIM_WORKSPACE_ID) workspaceId: string,
  //   @Param('workspaceId') workspaceIdInParam: string,
  // ) {
  //   return this.followersService.isUserOrWorkspaceFollowingParticularWorkspaceById(
  //     workspaceId ?? userId,
  //     workspaceIdInParam,
  //   );
  // }
}
