import { AccountType } from '@/constants/users';

export enum PostActiveStatus {
  ACTIVE = 1,
  INACTIVE = 0,
}

export enum PostStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  SCHEDULED = 'scheduled',
}

export enum PostType {
  TEXT = 'text',
  MEDIA = 'media',
  ARTICLE = 'article',
  POLL = 'poll',
  QUESTION = 'question',
}

export const PostTypeJoinFields: Record<
  PostType.QUESTION | PostType.ARTICLE | PostType.POLL,
  string
> = {
  [PostType.QUESTION]: 'questionPost',
  [PostType.ARTICLE]: 'articlePost',
  [PostType.POLL]: 'pollPost',
  // don't have table
  // [PostType.TEXT]: 'textPost',
  // [PostType.MEDIA]: 'mediaPost',
};

// Enum for community type (for reference, not used as DB enum)
export enum CommunityType {
  PROFESSIONAL = AccountType.PROFESSIONAL,
  PUBLIC = AccountType.PUBLIC,
  BOTH = 'BOTH',
}

export const MIN_LENGTH = 3;
export const MAX_LENGTH = 3000;

export const MEDIA_CATEGORY = {
  IMAGE: 'image',
  VIDEO: 'video',
  DOCUMENT: 'document',
} as const;

export type MediaCategoryType = (typeof MEDIA_CATEGORY)[keyof typeof MEDIA_CATEGORY]; // 'image' | 'video' | 'document'

// Define the structure for individual media type info
type MediaInfo = {
  readonly accept: readonly string[];
  readonly extensions: Record<string, string>;
  readonly maxSize: number;
  readonly maxFiles: number;
};

// Define the structure for the entire MEDIA_TYPES_INFO object
type MediaInfoMap = Record<MediaCategoryType, MediaInfo>;

export const ENTITY_TYPES = {
  POST: 'post',
  ARTICLE: 'article',
  PROFILE: 'profile',
} as const;

export type EntityTypesType = (typeof ENTITY_TYPES)[keyof typeof ENTITY_TYPES];

export const MIN_FILES = 1;
export const MAX_FILES = 20;

export const MEDIA_TYPES_INFO: MediaInfoMap = {
  [MEDIA_CATEGORY.IMAGE]: {
    accept: ['image/jpeg', 'image/gif', 'image/jpg', 'image/png', 'image/webp'],
    extensions: {
      'image/jpeg': 'jpeg',
      'image/gif': 'gif',
      'image/jpg': 'jpg',
      'image/png': 'png',
      'image/webp': 'webp',
    },
    maxSize: 2097152, // 2 MB in bytes
    maxFiles: MAX_FILES,
  } as const,

  [MEDIA_CATEGORY.VIDEO]: {
    accept: [
      'video/mp4',
      'video/avi',
      'video/webm',
      'video/x-ms-wmv',
      'video/x-flv',
      'video/mpeg',
      'video/quicktime',
      'video/x-m4v',
    ],
    extensions: {
      'video/mp4': 'mp4',
      'video/avi': 'avi',
      'video/webm': 'webm',
      'video/x-ms-wmv': 'wmv',
      'video/x-flv': 'flv',
      'video/mpeg': 'mpeg',
      'video/quicktime': 'mov',
      'video/x-m4v': 'm4v',
    },
    maxSize: 104857600, // 100 MB in bytes
    maxFiles: MAX_FILES,
  } as const,

  [MEDIA_CATEGORY.DOCUMENT]: {
    accept: [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'text/plain',
    ],
    extensions: {
      'application/pdf': 'pdf',
      'application/msword': 'doc',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx',
      'application/vnd.ms-excel': 'xls',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'xlsx',
      'application/vnd.ms-powerpoint': 'ppt',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'pptx',
      'text/plain': 'txt',
    },
    maxSize: 10485760, // 10 MB in bytes
    maxFiles: MAX_FILES, // Limiting document uploads to 5 files
  } as const,
};

export enum ReportedReason {
  SPAM = 'spam',
  HARASSMENT = 'harassment',
  INAPPROPRIATE_CONTENT = 'inappropriate content',
}

export enum ReportedPostStatus {
  ARCHIVED = -1,
  INACTIVE = 0,
  ACTIVE = 1,
}

// Constants for article content length
export const ARTICLE_CONTENT_MIN_LENGTH = 100; // Minimum 100 characters for article content
export const ARTICLE_CONTENT_MAX_LENGTH = 50000; // Maximum 50,000 characters for rich text content
