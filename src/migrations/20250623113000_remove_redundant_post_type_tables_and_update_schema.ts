import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // Drop text_posts table
  await knex.schema.dropTable('text_posts');
  // Drop media_posts table
  await knex.schema.dropTable('media_posts');
}

export async function down(knex: Knex): Promise<void> {
  // Recreate text_posts table
  await knex.schema
    .createTable('text_posts', (t) => {
      t.uuid('post_id', { primaryKey: true })
        .notNullable()
        .references('id')
        .inTable('posts')
        .onDelete('CASCADE')
        .index('index_text_posts_on_post_id');
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
        CREATE TRIGGER text_posts_updated_at BEFORE UPDATE
        ON text_posts FOR EACH ROW EXECUTE PROCEDURE 
        update_updated_at_column();
      `,
    );

  // Recreate media_posts table
  await knex.schema
    .createTable('media_posts', (t) => {
      t.uuid('post_id', { primaryKey: true })
        .notNullable()
        .references('id')
        .inTable('posts')
        .onDelete('CASCADE')
        .index('index_media_posts_on_post_id');
      t.timestamp('created_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
      t.timestamp('updated_at').notNullable().defaultTo(knex.raw('CURRENT_TIMESTAMP'));
    })
    .raw(
      `
        CREATE TRIGGER media_posts_updated_at BEFORE UPDATE
        ON media_posts FOR EACH ROW EXECUTE PROCEDURE 
        update_updated_at_column();
      `,
    );

  // Repopulate text_posts from posts where postType = 'text'
  await knex.raw(`
    INSERT INTO text_posts (post_id, created_at, updated_at)
    SELECT id, created_at, updated_at
    FROM posts
    WHERE post_type = 'text'
  `);

  // Repopulate media_posts from posts where postType = 'media'
  await knex.raw(`
    INSERT INTO media_posts (post_id, created_at, updated_at)
    SELECT id, created_at, updated_at
    FROM posts
    WHERE post_type = 'media'
  `);
}
