import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  // 1. Add new field to posts (nullable for safe migration)
  await knex.schema.alterTable('posts', (t) => {
    t.text('content').notNullable().defaultTo('');
  });

  // 2. Migrate data from post type tables to posts.content
  // Text posts
  await knex.raw(`
    UPDATE posts p
    SET content = tp.content
    FROM text_posts tp
    WHERE p.id = tp.post_id
  `);
  // Media posts
  await knex.raw(`
    UPDATE posts p
    SET content = mp.caption
    FROM media_posts mp
    WHERE p.id = mp.post_id AND mp.caption IS NOT NULL
      AND (p.content IS NULL OR p.content = '')
  `);
  // Article posts
  await knex.raw(`
    UPDATE posts p
    SET content = ap.summary
    FROM article_posts ap
    WHERE p.id = ap.post_id AND ap.summary IS NOT NULL
      AND (p.content IS NULL OR p.content = '')
  `);
  // Question posts
  await knex.raw(`
    UPDATE posts p
    SET content = qp.question
    FROM question_posts qp
    WHERE p.id = qp.post_id AND qp.question IS NOT NULL
      AND (p.content IS NULL OR p.content = '')
  `);

  // 3. Optionally, make content NOT NULL if you want to enforce it
  // await knex.schema.alterTable('posts', (t) => {
  //   t.text('content').notNullable().alter();
  // });

  // 4. Remove old fields from post type tables
  await knex.schema.alterTable('text_posts', (t) => {
    t.dropColumn('content');
  });
  await knex.schema.alterTable('media_posts', (t) => {
    t.dropColumn('caption');
  });
  await knex.schema.alterTable('article_posts', (t) => {
    t.dropColumn('summary');
  });
  await knex.schema.alterTable('question_posts', (t) => {
    t.dropColumn('question');
  });
}

export async function down(knex: Knex): Promise<void> {
  // 1. Add the removed columns back
  await knex.schema.alterTable('text_posts', (t) => {
    t.text('content').notNullable().defaultTo('');
  });
  await knex.schema.alterTable('media_posts', (t) => {
    t.text('caption');
  });
  await knex.schema.alterTable('article_posts', (t) => {
    t.text('summary').notNullable().defaultTo('');
  });
  await knex.schema.alterTable('question_posts', (t) => {
    t.text('question').notNullable().defaultTo('');
  });

  // 2. Migrate data back from posts.content
  await knex.raw(`
    UPDATE text_posts tp
    SET content = p.content
    FROM posts p
    WHERE tp.post_id = p.id
  `);
  await knex.raw(`
    UPDATE media_posts mp
    SET caption = p.content
    FROM posts p
    WHERE mp.post_id = p.id
  `);
  await knex.raw(`
    UPDATE article_posts ap
    SET summary = p.content
    FROM posts p
    WHERE ap.post_id = p.id
  `);
  await knex.raw(`
    UPDATE question_posts qp
    SET question = p.content
    FROM posts p
    WHERE qp.post_id = p.id
  `);

  // 3. Remove the content field from posts
  await knex.schema.alterTable('posts', (t) => {
    t.dropColumn('content');
  });
}
