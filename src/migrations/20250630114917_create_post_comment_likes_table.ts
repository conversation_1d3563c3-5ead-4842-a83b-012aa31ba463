import type { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  await knex.schema
    .createTable('post_comment_likes', (t) => {
      t.primary(['post_comment_id', 'entity_id']);
      t.uuid('post_comment_id')
        .notNullable()
        .references('id')
        .inTable('post_comments')
        .onDelete('CASCADE')
        .index('index_post_comment_likes_on_post_comment_id');
      t.text('entity_type').notNullable();
      t.uuid('entity_id').notNullable().index('index_post_comment_likes_on_entity_id');
      t.integer('status').notNullable();
      t.timestamp('created_at').notNullable().defaultTo(knex.fn.now());
      t.timestamp('updated_at').notNullable().defaultTo(knex.fn.now());

      // Composite unique constraint to prevent duplicate likes
      t.unique(['entity_id', 'entity_type', 'post_comment_id']);
    })
    .raw(
      `
CREATE TRIGGER post_comment_likes_updated_at BEFORE UPDATE
ON post_comment_likes FOR EACH ROW EXECUTE PROCEDURE 
update_updated_at_column();
`,
    );
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTable('post_comment_likes');
}
