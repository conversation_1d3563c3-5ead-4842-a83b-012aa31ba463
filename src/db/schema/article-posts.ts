import { relations } from 'drizzle-orm';
import { pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';

import { posts } from './posts';

export const articlePosts = pgTable('article_posts', {
  postId: uuid('post_id')
    .primaryKey()
    .notNull()
    .references(() => posts.id, { onDelete: 'cascade' }),
  title: text('title').notNull(),
  body: text('body').notNull(),
  coverImagePath: text('cover_image_path'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const articlePostsRelations = relations(articlePosts, ({ one }) => ({
  post: one(posts, {
    fields: [articlePosts.postId],
    references: [posts.id],
  }),
}));

export type ArticlePost = typeof articlePosts.$inferSelect;
export type NewArticlePost = typeof articlePosts.$inferInsert;
