import { relations, sql } from 'drizzle-orm';
import { customType, integer, pgEnum, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';

import { PostStatus, PostType, CommunityType } from '@/constants/posts';
import { PrimarySpeciality } from '@/constants/workspaces';

import { enumToPgEnum } from '@/utils/database';

import { users } from './users';
import { workspaces } from './workspaces';
import { postCountries } from './post-countries';
import { postComments } from './post-comments';
import { postLikes } from './post-likes';
import { postViews } from './post-views';
import { postTags } from './post-tags';
import { postMedias } from './post-medias';
import { postNotifications } from './post-notifications';
import { opportunities } from './opportunities';
import { reportedPosts } from './reported-posts';
import { postPolls } from './post-polls';
import { questionPosts } from './question-posts';
import { articlePosts } from './article-posts';

const postStatusEnum = pgEnum('post_status', enumToPgEnum(PostStatus));
const postTypeEnum = pgEnum('post_type', enumToPgEnum(PostType));
const communityEnum = pgEnum('community', enumToPgEnum(CommunityType));
const audienceEnum = pgEnum('audience', enumToPgEnum(PrimarySpeciality));

export const postsRelationsNames = {
  publisher: 'publisher',
  updatedBy: 'updatedBy',
};

const tsvector = customType<{
  data: string;
  driverData: string;
}>({
  dataType() {
    return 'tsvector';
  },
});

export const posts = pgTable('posts', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  publishedById: uuid('published_by_id')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  updatedById: uuid('updated_by_id')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  workspaceId: uuid('workspace_id')
    .notNull()
    .references(() => workspaces.id, {
      onDelete: 'cascade',
    }),
  opportunityId: uuid('opportunity_id').references(() => opportunities.id, { onDelete: 'cascade' }),
  postStatus: postStatusEnum('post_status').notNull(),
  community: communityEnum('community').notNull(),
  audience: audienceEnum('audience').notNull(),
  tags: text('tags').array().default([]),
  postType: postTypeEnum('post_type').notNull(),
  postScheduleDate: timestamp('post_schedule_date').notNull(),
  status: integer('status').notNull().default(1),
  content: text('content').notNull().default(''),
  contentTsv: tsvector('content_tsv'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const postsRelations = relations(posts, ({ one, many }) => ({
  publishedBy: one(users, {
    fields: [posts.publishedById],
    references: [users.id],
    relationName: postsRelationsNames.publisher,
  }),
  updatedBy: one(users, {
    fields: [posts.updatedById],
    references: [users.id],
    relationName: postsRelationsNames.updatedBy,
  }),
  workspace: one(workspaces, {
    fields: [posts.workspaceId],
    references: [workspaces.id],
  }),
  opportunity: one(opportunities, {
    fields: [posts.opportunityId],
    references: [opportunities.id],
  }),
  postCountries: many(postCountries),
  postComments: many(postComments),
  postLikes: many(postLikes),
  postViews: many(postViews),
  postTags: many(postTags),
  postMedias: many(postMedias),
  postNotifications: many(postNotifications),
  postPolls: many(postPolls),
  reports: many(reportedPosts),
  articlePost: one(articlePosts, {
    fields: [posts.id],
    references: [articlePosts.postId],
  }),
  questionPost: one(questionPosts, {
    fields: [posts.id],
    references: [questionPosts.postId],
  }),
}));

export type Post = typeof posts.$inferSelect;
export type NewPost = typeof posts.$inferInsert;
