import { relations } from 'drizzle-orm';
import { pgTable, timestamp, uuid } from 'drizzle-orm/pg-core';

import { posts } from './posts';
import { postComments } from './post-comments';

export const questionPosts = pgTable('question_posts', {
  postId: uuid('post_id')
    .primaryKey()
    .notNull()
    .references(() => posts.id, { onDelete: 'cascade' }),
  pinnedCommentId: uuid('pinned_comment_id').references(() => postComments.id, {
    onDelete: 'set null',
  }),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const questionPostsRelations = relations(questionPosts, ({ one }) => ({
  post: one(posts, {
    fields: [questionPosts.postId],
    references: [posts.id],
  }),
  comment: one(postComments, {
    fields: [questionPosts.pinnedCommentId],
    references: [postComments.id],
  }),
}));

export type QuestionPost = typeof questionPosts.$inferSelect;
export type NewQuestionPost = typeof questionPosts.$inferInsert;
