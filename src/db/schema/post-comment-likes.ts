import { pgTable, uuid, timestamp, integer, pgEnum, primaryKey } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';

import { enumToPgEnum } from '@/utils/database';
import { EntityType } from '@/constants/user-types';
import { postComments } from './post-comments';

const entityTypeEnum = pgEnum('entity_type', enumToPgEnum(EntityType));

export const postCommentLikes = pgTable(
  'post_comment_likes',
  {
    postCommentId: uuid('post_comment_id')
      .notNull()
      .references(() => postComments.id, { onDelete: 'cascade' }),
    entityType: entityTypeEnum('entity_type').notNull(),
    entityId: uuid('entity_id').notNull(),
    status: integer('status').notNull(),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow(),
  },
  (table) => ({
    pk: primaryKey({ name: 'id', columns: [table.postCommentId, table.entityId] }),
  }),
);

export const postCommentLikesRelations = relations(postCommentLikes, ({ one }) => ({
  comment: one(postComments, {
    fields: [postCommentLikes.postCommentId],
    references: [postComments.id],
  }),
}));

export type PostCommentLike = typeof postCommentLikes.$inferSelect;
export type NewPostCommentLike = typeof postCommentLikes.$inferInsert;
