import { relations, sql } from 'drizzle-orm';
import { integer, pgTable, smallint, text, timestamp, uuid, boolean } from 'drizzle-orm/pg-core';

import { posts } from './posts';

export const postMedias = pgTable('post_medias', {
  id: uuid('id')
    .primaryKey()
    .default(sql`uuid_generate_v4()`),
  postId: uuid('post_id')
    .notNull()
    .references(() => posts.id, { onDelete: 'cascade' }),
  mediaPath: text('media_path').notNull(),
  mediaType: text('media_type').notNull(),
  altText: text('alt_text'),
  order: smallint('order').notNull().default(0),
  isCoverPic: boolean('is_cover_pic').default(false),
  status: integer('status').notNull().default(1),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const postMediasRelations = relations(postMedias, ({ one }) => ({
  post: one(posts, {
    fields: [postMedias.postId],
    references: [posts.id],
  }),
}));

export type PostMedia = typeof postMedias.$inferSelect;
export type NewPostMedia = typeof postMedias.$inferInsert;
