import { OTP_LENGTH } from '@/constants/otp';

import configuration from '@/config/configuration';

/**
 * Generates a random numeric OTP of specified length
 * @returns A string containing the OTP
 */
export function generateOtp(): string {
  const config = configuration();

  // In development environment, return a fixed OTP
  if (config.environment.isDevelop) {
    return '123456';
  }

  // Generate a random number with the specified number of digits
  const min = Math.pow(10, OTP_LENGTH - 1);
  const max = Math.pow(10, OTP_LENGTH) - 1;
  const otp = Math.floor(min + Math.random() * (max - min + 1)).toString();

  return otp;
}

/**
 * Calculates the expiration time for an OTP
 * @param expirationMinutes Number of minutes until the OTP expires
 * @returns Date object representing the expiration time
 */
export function calculateOtpExpiry(expirationMinutes: number): Date {
  const expiryDate = new Date();
  expiryDate.setMinutes(expiryDate.getMinutes() + expirationMinutes);
  return expiryDate;
}
