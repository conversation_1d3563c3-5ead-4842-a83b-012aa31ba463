import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';

import { ALLOW_NON_VERIFIED_WORKSPACE_ACCESS } from '@/constants/system';

import { WorkspacesService } from '@/modules/workspaces/workspaces.service';

import { workspaceNotVerified } from '@/exceptions/workspaces';

import { ACTIVE_WORKSPACE_METADATA_KEY } from '@/decorators/active-workspace.decorator';

import { isActiveWorkspace } from '@/utils/workspaces';

@Injectable()
export class ActiveWorkspaceGuard implements CanActivate {
  constructor(
    private readonly workspaceService: WorkspacesService,
    private reflector: Reflector,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const isAllowActiveWorkspaceOnly = this.reflector.getAllAndOverride<boolean>(
      ACTIVE_WORKSPACE_METADATA_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (!isAllowActiveWorkspaceOnly || ALLOW_NON_VERIFIED_WORKSPACE_ACCESS) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const { workspaceId } = request.user;

    if (workspaceId) {
      const workspace = await this.workspaceService.findOneWorkspaceByWorkspaceId(
        workspaceId,
        true,
      );
      if (!isActiveWorkspace(workspace)) {
        throw workspaceNotVerified();
      }
    }

    return true;
  }
}
